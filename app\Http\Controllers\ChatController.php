<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\User;
use App\Events\MessageSent;
use Illuminate\Http\Request;

class ChatController extends Controller
{
    public function index()
    {
        $me = session('user_id');
        $users = User::where('id', '!=', $me)->get();
        return view('chat', compact('users'));
    }

    public function getMessages($id)
    {
        $me = session('user_id');
        return Message::where(function ($q) use ($me, $id) {
            $q->where('sender_id', $me)->where('receiver_id', $id);
        })->orWhere(function ($q) use ($me, $id) {
            $q->where('sender_id', $id)->where('receiver_id', $me);
        })->with('sender')->get();
    }

    public function send(Request $request)
    {
        $message = Message::create([
            'sender_id' => session('user_id'),
            'receiver_id' => $request->receiver_id,
            'message' => $request->message
        ]);

        broadcast(new MessageSent($message))->toOthers();
        return $message;
    }
}
