[2025-06-18 05:56:31] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:56:50] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:57:16] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:57:51] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:57:51] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:57:54] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:29] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 22)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:29] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 22)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:29] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 22)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:50] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:50] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 23)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 05:58:51] local.ERROR: Class "Pusher\Pusher" not found {"exception":"[object] (Error(code: 0): Class \"Pusher\\Pusher\" not found at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php:306)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(295): Illuminate\\Broadcasting\\BroadcastManager->pusher(Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(262): Illuminate\\Broadcasting\\BroadcastManager->createPusherDriver(Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(233): Illuminate\\Broadcasting\\BroadcastManager->resolve('pusher')
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(222): Illuminate\\Broadcasting\\BroadcastManager->get('pusher')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Broadcasting\\BroadcastManager.php(490): Illuminate\\Broadcasting\\BroadcastManager->driver()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Broadcasting\\BroadcastManager->__call('channel', Array)
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\routes\\channels.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('channel', Array)
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\app\\Providers\\BroadcastServiceProvider.php(17): require('C:\\\\INCT\\\\Parth\\\\p...')
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\BroadcastServiceProvider->boot()
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\BroadcastServiceProvider))
#15 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\BroadcastServiceProvider), 22)
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-06-18 06:10:11] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'laravel' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'laravel' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#4 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#15 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) at C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select table_na...', Array)
#9 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#10 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#11 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#12 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#13 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#14 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#15 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#16 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#17 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#18 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#19 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#20 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#21 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#22 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#23 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\INCT\\Parth\\practice projects\\laravel-pusher\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
