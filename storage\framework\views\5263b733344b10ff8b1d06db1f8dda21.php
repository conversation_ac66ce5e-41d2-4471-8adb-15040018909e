<!-- Toastr CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
<!-- Toastr JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<h3>Users:</h3>
<ul>
    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $u): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li><a href="#" onclick="loadMessages(<?php echo e($u->id); ?>, '<?php echo e($u->name); ?>')"><?php echo e($u->name); ?></a>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>

<div id="chat"></div>
<input type="text" id="msg">
<button onclick="sendMessage()">Send</button>

<script src="https://js.pusher.com/7.2/pusher.min.js"></script>
<!-- Optional: Toast sound -->
<audio id="notifySound" src="https://notificationsounds.com/storage/sounds/file-sounds-1152-pristine.mp3"
    preload="auto"></audio>

<script>
    let receiverId = null;
    let myId = <?php echo e(session('user_id')); ?>;

    function loadMessages(id, name) {
        receiverId = id;
        fetch(`/messages/${id}`).then(res => res.json()).then(data => {
            document.getElementById('chat').innerHTML = '';
            data.forEach(msg => {
                document.getElementById('chat').innerHTML +=
                    `<div><b>${msg.sender.name}:</b> ${msg.message}</div>`;
            });
        });
    }

    function sendMessage() {
        let msg = document.getElementById('msg').value;
        if (!receiverId || !msg.trim()) return;

        fetch('/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            body: JSON.stringify({
                message: msg,
                receiver_id: receiverId
            })
        }).then(() => {
            document.getElementById('chat').innerHTML += `<div><b>You:</b> ${msg}</div>`;
            document.getElementById('msg').value = '';
        });
    }

    Pusher.logToConsole = false;
    let pusher = new Pusher('<?php echo e(env('PUSHER_APP_KEY')); ?>', {
        cluster: '<?php echo e(env('PUSHER_APP_CLUSTER')); ?>',
        authEndpoint: '/broadcasting/auth',
        auth: {
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            }
        }
    });

    let channel = pusher.subscribe('private-chat.' + myId);
    channel.bind('message.received', data => {
        const senderName = data.message.sender.name;
        const senderId = data.message.sender_id;
        const messageText = data.message.message;

        if (senderId == receiverId) {
            // Chat window is open with the sender — show message inline
            document.getElementById('chat').innerHTML +=
                `<div><b>${senderName}:</b> ${messageText}</div>`;
        } else {
            // Message from another user — show real-time toast
            toastr.info(`New message from ${senderName}: ${messageText}`);
            document.getElementById('notifySound').play();
        }
    });
</script>
<?php /**PATH C:\INCT\Parth\practice projects\laravel-pusher\resources\views/chat.blade.php ENDPATH**/ ?>