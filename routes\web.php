<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AuthController;

Route::get('/register', [AuthController::class, 'showRegister']);
Route::post('/register', [AuthController::class, 'register']);
Route::get('/', [AuthController::class, 'showLogin']);
Route::post('/login', [AuthController::class, 'login']);
Route::get('/logout', [AuthController::class, 'logout']);

Route::middleware('auth.custom')->group(function () {
    Route::get('/chat', [ChatController::class, 'index']);
    Route::get('/messages/{id}', [ChatController::class, 'getMessages']);
    Route::post('/send', [ChatController::class, 'send']);
});
