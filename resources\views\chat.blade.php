<!-- Toastr CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
<!-- Toastr JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<h3>Users:</h3>
<ul>
    @foreach ($users as $u)
        <li><a href="#" onclick="loadMessages({{ $u->id }}, '{{ $u->name }}')">{{ $u->name }}</a>
        </li>
    @endforeach
</ul>

<div id="chat"></div>
<input type="text" id="msg">
<button onclick="sendMessage()">Send</button>

<script src="https://js.pusher.com/7.2/pusher.min.js"></script>
<!-- Optional: Toast sound -->
<audio id="notifySound" src="https://notificationsounds.com/storage/sounds/file-sounds-1152-pristine.mp3"
    preload="auto"></audio>

<script>
    let receiverId = null;
    let myId = {{ session('user_id') }};

    function loadMessages(id, name) {
        receiverId = id;
        fetch(`/messages/${id}`).then(res => res.json()).then(data => {
            document.getElementById('chat').innerHTML = '';
            data.forEach(msg => {
                document.getElementById('chat').innerHTML +=
                    `<div><b>${msg.sender.name}:</b> ${msg.message}</div>`;
            });
        });
    }

    function sendMessage() {
        let msg = document.getElementById('msg').value;
        if (!receiverId || !msg.trim()) return;

        fetch('/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                message: msg,
                receiver_id: receiverId
            })
        }).then(() => {
            document.getElementById('chat').innerHTML += `<div><b>You:</b> ${msg}</div>`;
            document.getElementById('msg').value = '';
        });
    }

    Pusher.logToConsole = false;
    let pusher = new Pusher('{{ env('PUSHER_APP_KEY') }}', {
        cluster: '{{ env('PUSHER_APP_CLUSTER') }}',
        authEndpoint: '/broadcasting/auth',
        auth: {
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        }
    });

    let channel = pusher.subscribe('private-chat.' + myId);
    channel.bind('message.received', data => {
        const senderName = data.message.sender.name;
        const senderId = data.message.sender_id;
        const messageText = data.message.message;

        if (senderId == receiverId) {
            // Chat window is open with the sender — show message inline
            document.getElementById('chat').innerHTML +=
                `<div><b>${senderName}:</b> ${messageText}</div>`;
        } else {
            // Message from another user — show real-time toast
            toastr.info(`New message from ${senderName}: ${messageText}`);
            document.getElementById('notifySound').play();
        }
    });
</script>
